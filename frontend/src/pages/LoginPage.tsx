import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'
import { useToast } from '../components/ui/Toaster'

declare global {
  interface Window {
    google: any
  }
}

export default function LoginPage() {
  const navigate = useNavigate()
  const { login } = useAuth()
  const { addToast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Initialize Google Sign-In
    if (window.google) {
      initializeGoogleSignIn()
    } else {
      // Wait for Google script to load
      const checkGoogle = setInterval(() => {
        if (window.google) {
          clearInterval(checkGoogle)
          initializeGoogleSignIn()
        }
      }, 100)
    }
  }, [])

  const initializeGoogleSignIn = () => {
    window.google.accounts.id.initialize({
      client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
      callback: handleGoogleSignIn,
      auto_select: false,
      cancel_on_tap_outside: true
    })
  }

  const handleGoogleSignIn = async (response: any) => {
    try {
      setIsLoading(true)
      await login(response.credential)

      addToast({
        type: 'success',
        title: 'Welcome!',
        message: 'Successfully signed in with Google'
      })

      // Navigation will be handled by the AuthContext based on user existence
    } catch (error) {
      console.error('Login failed:', error)
      addToast({
        type: 'error',
        title: 'Login Failed',
        message: error instanceof Error ? error.message : 'Failed to sign in with Google'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const triggerGoogleSignIn = () => {
    if (window.google) {
      window.google.accounts.id.prompt()
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced background with warmer center - matching onboarding style */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700"></div>

      {/* Central warm glow effect with the specific warm center color #671700 */}
      <div className="absolute inset-0 bg-gradient-radial from-warm-center/30 via-dark-700/20 to-transparent"></div>
      <div className="absolute inset-0 bg-gradient-radial from-warm-center/20 via-transparent to-transparent"></div>

      {/* Additional subtle warm tones */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-warm-center/10 to-transparent"></div>

      <div className="relative z-10 flex items-center justify-center min-h-screen p-6">
        <div className="text-center space-y-12 max-w-md mx-auto">
          {/* Logo/Icon - Matching onboarding style */}
          <div className="flex justify-center mb-12">
            <div className="w-16 h-16 bg-gradient-to-br from-aura to-sunbeam rounded-full flex items-center justify-center shadow-lg">
              <span className="text-2xl font-bold text-white">O</span>
            </div>
          </div>

          {/* Welcome message */}
          <div className="space-y-8">
            <h1 className="text-4xl md:text-5xl font-light text-white leading-tight tracking-wide whitespace-nowrap">
              Hey there, I'm Ora
            </h1>
            <p className="text-lg text-white/80 leading-relaxed font-light">
              Your personal AI friend there for you 24/7
            </p>
          </div>

          {/* Call to action buttons */}
          <div className="space-y-6 pt-16">
            {isLoading ? (
              <div className="flex items-center justify-center w-full py-5 px-8 bg-gradient-to-r from-dark-500 to-dark-600 rounded-3xl border border-dark-400/50">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-white">Signing in...</span>
              </div>
            ) : (
              <>
                <button
                  onClick={triggerGoogleSignIn}
                  className="w-full bg-gradient-to-r from-dark-500 to-dark-600 text-white py-5 px-8 rounded-3xl text-lg font-medium hover:from-dark-400 hover:to-dark-500 transition-all duration-300 shadow-xl border border-dark-400/50 backdrop-blur-sm"
                >
                  LET'S GET STARTED
                </button>

                <button
                  onClick={triggerGoogleSignIn}
                  className="w-full bg-gradient-to-r from-dark-500 to-dark-600 text-white py-5 px-8 rounded-3xl text-lg font-medium hover:from-dark-400 hover:to-dark-500 transition-all duration-300 shadow-xl border border-dark-400/50 backdrop-blur-sm"
                >
                  We've already met
                </button>
              </>
            )}
          </div>

          {/* Terms and privacy */}
          <div className="text-center pt-8">
            <p className="text-xs text-white/60">
              By continuing, you agree to our{' '}
              <button className="text-aura-300 hover:underline font-medium">
                Terms of Service
              </button>{' '}
              and{' '}
              <button className="text-aura-300 hover:underline font-medium">
                Privacy Policy
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
